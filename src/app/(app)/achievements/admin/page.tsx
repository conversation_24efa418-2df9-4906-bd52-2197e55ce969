import prisma from "~/server/db/prisma";
import Card from "~/component/card";
import AchievementEventForm from "~/app/(app)/achievements/admin/AchievementEventForm";
import AchievementEventCreateForm from "~/app/(app)/achievements/admin/AchievmentEventCreateForm";
import Accordion from "~/app/(app)/component/Accordion";


export default async function AdminEventsPage() {

  function toLocalInputValue(date: Date | null): string | null {
    if (!date) return null;
    const pad = (n: number) => String(n).padStart(2, "0");
    return (
      date.getFullYear() +
      "-" + pad(date.getMonth() + 1) +
      "-" + pad(date.getDate()) +
      "T" + pad(date.getHours()) +
      ":" + pad(date.getMinutes())
    );
  }

  const rawEvents = await prisma.achievementEvent.findMany({
    where: {
      legacy: false, // 👈 nur aktuelle Events
    },
    orderBy: { createdAt: "desc" },
    select: {
      id: true,
      code: true,
      title: true,
      description: true,
      badgeImageUrl: true,
      startsAt: true,
      endsAt: true,
      visible: true,
      legacy: true, // 👈 mitladen, damit der <PERSON>ton weiß, ob schon Legacy
    },
  });


  const events = rawEvents.map((e) => ({
    ...e,
    startsAt: toLocalInputValue(e.startsAt),
    endsAt: toLocalInputValue(e.endsAt),
  }));

  return (
    <Card>
      <div className="w-full min-h-dvh p-4 space-y-6">
        <h1 className="text-xl font-semibold">Events – Admin</h1>

        {/* Create Form */}
        <AchievementEventCreateForm />

        {/* List Forms */}
        <Accordion summary={"Liste aller aktuellen Events"} defaultExpanded={false}>
          <div className="space-y-6">

          {events.map((event) => (
          <div key={event.id} className="rounded-lg border p-4 bg-white">
            <AchievementEventForm initialEvent={event} />
          </div>

        ))}
            </div>
          </Accordion>
      </div>
    </Card>
  );
}
