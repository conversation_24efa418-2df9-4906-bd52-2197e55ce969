"use client";

import React, { useEffect, useMemo, useState } from "react";
import Button from "~/component/button";
import { useRouter } from "next/navigation";
import Card from "~/component/card";
import SessionsBadge from "~/app/(app)/achievements/Badges/SessionsBadge";
import StreakBadge from "~/app/(app)/achievements/Badges/StreakBadge";
import NachtEule from "~/app/(app)/achievements/Badges/NachtEuleBadge";
import Co2Savings from "~/app/(app)/achievements/Badges/CO2SavingsBadge";
import { PartnerLocationsBadge } from "~/app/(app)/achievements/Badges/PartnerLocationsBadge";
import EulektroStandorteBadge from "~/app/(app)/achievements/Badges/EulektroStandorteBadge";
import AchievementEvents from "~/app/(app)/component/AchievmentEvents";
import Accordion from "~/app/(app)/component/Accordion";
import BadgeBase from "~/app/(app)/component/BadgeBase";
import Modal from "~/app/(app)/component/ModalBadge";
import {UserDataAchievment} from "~/types/achievment/bagdesTypes";
import {normalizePublicPath} from "~/utils/path/normalizePath";






export default function AchievementsForm({ userData }: { userData?: UserDataAchievment }) {
  const router = useRouter();
  const [submitting, setSubmitting] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);



  /** Klick: „Ich möchte Trophäen sammeln“ */
  async function handleCollector() {
    setErrorMsg(null);
    setSubmitting(true);
    try {
      const res = await fetch("/api/achievments/newCollector", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId: userData?.userId }),
      });
      if (!res.ok) {
        const text = await res.text().catch(() => "");
        throw new Error(text || "Fehler beim Anlegen als Collector");
      }
      router.push("/achievements");
      router.refresh();
    } catch (err) {
      console.error(err);
      setErrorMsg("Konnte Collector nicht speichern. Bitte später erneut versuchen.");
    } finally {
      setSubmitting(false);
    }
  }

  const firedOnce = React.useRef(false);

  //das hier komm noch in ein cronjob oder so sowas in der Art!!!!!!!!!!!!!!!!!!!!!
  React.useEffect(() => {
    if (firedOnce.current) return;          // verhindert doppeltes Feuern im Dev
    firedOnce.current = true;

    fetch("/api/achievments/events/progress/recalc", {
      method: "POST",
      cache: "no-store",
    })
      .then(async (r) => {
        if (!r.ok) {
          const msg = await r.text().catch(() => "");
          console.error("Recalc failed:", r.status, msg);
        }
      })
      .catch((err) => console.error("Recalc error:", err));
  }, []);

    function FinishedBadgeItem({ raw, label }: { raw: string; label?: string }) {
        const [open, setOpen] = React.useState(false);
        const src = normalizePublicPath(raw);
        const safeLabel = label ?? src.split("/").pop()?.split(".")[0] ?? "Badge";

        return (
            <>
                <BadgeBase
                    imgSrc={src}
                    label={safeLabel}
                    size="xl"
                    progress={0}
                    locked={false}
                    onClick={() => setOpen(true)}
                    infoMode="button"
                    infoButtonLabel="Badge-Info anzeigen"
                />
                <Modal open={open} onClose={() => setOpen(false)} title={safeLabel}>
                    <div className="flex flex-col items-center gap-3">
                        <img src={src} alt={safeLabel} className="h-28 w-28 rounded-full object-cover" style={{ width: 300, height: 300 }} />
                        <p className="text-sm text-gray-700 text-center">
                            Glückwunsch! Diesen Badge hast du abgeschlossen. 🎉
                        </p>
                    </div>
                </Modal>
            </>
        );
    }


    /** Wenn nicht Collector -> Hinweis + Buttons */
  if (!userData?.isCollector) {
    return (
      <Card>
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Trophäen</h2>
        <p className="text-sm text-gray-600">Sammle Trophäen und nimm an Events Teil und Erhalte Boni und Rabatte.</p>

        {errorMsg && (
          <div className="rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-700">
            {errorMsg}
          </div>
        )}

        <div className="flex items-center gap-3">
          <Button
            type="button"
            onClick={handleCollector}
            disabled={submitting}
            className="px-4 py-2"
          >
            {submitting ? "Wird angelegt…" : "Ja, ich möchte Trophäen sammeln"}
          </Button>

          <Button
            type="button"
            onClick={() => router.push("/")}
            className="px-4 py-2 bg-gray-800 hover:brightness-110"
          >
            Abbrechen
          </Button>
        </div>
      </div>
        </Card>
    );
  }

  /** Collector -> komplettes Formular rendern */

    return (
    <div className="space-y-6">

      <Card header_left="Trophäen">


        {/* Badges */}
        <section aria-labelledby="badges" className="space-y-3">


          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3">
            <SessionsBadge cdrCount={userData?.cdrCount ?? 0} />
            <StreakBadge laengsteLadeStreak={userData?.laengsteLadeStreak ?? 0} />
            <NachtEule  nachtsLaden={userData?.nachtsLaden ?? 0}
                        extraInfo="Gezählt werden Ladevorgänge mit Startzeit zwischen 22:00–06:00."
                        />
            <EulektroStandorteBadge count={userData?.besuchteEulektroStandorte ?? 0}  />
            <Co2Savings co2Savings={userData?.co2Kg ?? 0}
                        extraInfo="Ein Elektroauto startet mit einem zusätzlichen CO₂-Rucksack aus der Batterieproduktion. Im Betrieb spart es jedoch gegenüber Benzin- und Dieselautos im Schnitt ca. 570 g CO₂ pro kWh Strom. Nach rund 9.000 kWh (≈ 50.000 km) ist der Herstellungsaufwand ausgeglichen – ab dann fährt das E-Auto klimafreundlicher.
                        Break-even: 9.000 kWh = ca. 5,1 t CO₂-Ersparnis ✅"/>
            <PartnerLocationsBadge count={userData?.besuchteStandorte ?? 0} />
          </div>
        </section>

          <section aria-labelledby="finished-badges" className="space-y-8">
              <h3 id="finished-badges" className="sr-only">Abgeschlossene Badges</h3>

              <Accordion
                  summary="Badges Abgeschlossener Events"
                  defaultExpanded={false}
                  id="finished-badges-accordion"
              >
                  {userData?.finishedBadgeImageUrls?.length ? (
                      <div className="flex flex-wrap items-center gap-4">
                          {userData.finishedBadgeImageUrls.map((raw, idx) => {
                              const label = normalizePublicPath(raw).split("/").pop()?.split(".")[0] ?? `Badge ${idx + 1}`;
                              return <FinishedBadgeItem key={`${raw}-${idx}`} raw={raw} label={label} />;
                          })}
                      </div>
                  ) : (
                      <p className="text-sm text-gray-600">Noch keine abgeschlossenen Badges vorhanden.</p>
                  )}
              </Accordion>
          </section>



          {/* Events */}
          <section aria-labelledby="events" className="mt-10">
              <h2 id="events" className="mb-2 text-sm font-medium text-gray-600">
                  Events
              </h2>
              <AchievementEvents />
          </section>
      </Card>
    </div>
  );
}
