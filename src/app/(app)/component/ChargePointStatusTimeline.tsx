"use client";

import React, { useMemo } from "react";

// Type definition basierend auf dem Prisma Schema
interface OccpChargePointStatusData {
  id: string;
  status: string;
  errorcode: string;
  vendorid: string;
  chargepointid: string;
  connectornumber: number;
  locationid: string;
  evseid: string;
  ocppTimestamp: string;
}

interface ChargePointStatusTimelineProps {
  statusData: OccpChargePointStatusData[];
  chargePointId?: string; // Optional filter für einen spezifischen ChargePoint
  timeRange?: {
    start: Date;
    end: Date;
  };
}

// Status-Farben mapping (aus ConnectorStateBar übernommen)
const colorMapping: Record<string, string> = {
  Available: "bg-green-300", // freundliches, etwas wärmeres Grün
  Charging: "bg-yellow-300", // gleichmäßiges Gelb
  SuspendedEV: "bg-orange-300", // identisch mit EVSE für Einheitlichkeit
  Preparing: "bg-gray-300", // neutraleres Grau
  SuspendedEVSE: "bg-orange-400", // gleich wie SuspendedEV für Klarheit
  Finishing: "bg-gray-200", // heller als Preparing → visuelle Abgrenzung
  Unavailable: "bg-red-300", // etwas heller als vorher, weniger aggressiv
  Faulted: "bg-red-400",
};

// Hilfsfunktion um Status-Farbe zu bekommen
const getStatusColor = (status: string): string => {
  return colorMapping[status] || "bg-gray-100";
};

// Hilfsfunktion um Zeitstempel zu formatieren
const formatTimestamp = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleString("de-DE", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return timestamp;
  }
};

// Hilfsfunktion um Dauer zwischen zwei Zeitpunkten zu berechnen
const calculateDuration = (start: string, end: string): string => {
  try {
    const startDate = new Date(start);
    const endDate = new Date(end);
    const diffMs = endDate.getTime() - startDate.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 60) {
      return `${diffMinutes}m`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      const minutes = diffMinutes % 60;
      return `${hours}h ${minutes}m`;
    }
  } catch {
    return "?";
  }
};

const ChargePointStatusTimeline: React.FC<ChargePointStatusTimelineProps> = ({
  statusData,
  chargePointId,
  timeRange,
}) => {
  // Daten filtern und sortieren
  const processedData = useMemo(() => {
    let filtered = statusData;

    // Nach ChargePoint filtern falls angegeben
    if (chargePointId) {
      filtered = filtered.filter((item) => item.chargepointid === chargePointId);
    }

    // Nach Zeitraum filtern falls angegeben
    if (timeRange) {
      filtered = filtered.filter((item) => {
        const itemDate = new Date(item.ocppTimestamp);
        return itemDate >= timeRange.start && itemDate <= timeRange.end;
      });
    }

    // Nach Zeitstempel sortieren (neueste zuerst)
    return filtered.sort((a, b) => 
      new Date(b.ocppTimestamp).getTime() - new Date(a.ocppTimestamp).getTime()
    );
  }, [statusData, chargePointId, timeRange]);

  // Daten nach ChargePoint gruppieren
  const groupedData = useMemo(() => {
    const groups: Record<string, OccpChargePointStatusData[]> = {};
    
    processedData.forEach((item) => {
      const key = `${item.chargepointid}-${item.connectornumber}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
    });

    return groups;
  }, [processedData]);

  if (processedData.length === 0) {
    return (
      <div className="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          ChargePoint Status Timeline
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Keine Status-Daten verfügbar für den ausgewählten Zeitraum.
        </p>
      </div>
    );
  }

  return (
    <div className="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
      <h3 className="mb-6 text-lg font-semibold text-gray-900 dark:text-white">
        ChargePoint Status Timeline
      </h3>
      
      <div className="space-y-6">
        {Object.entries(groupedData).map(([chargePointKey, events]) => {
          const [cpId, connectorNum] = chargePointKey.split('-');
          
          return (
            <div key={chargePointKey} className="border-l-4 border-gray-200 pl-4">
              <h4 className="mb-3 font-medium text-gray-900 dark:text-white">
                {cpId} - Connector {connectorNum}
              </h4>
              
              <div className="space-y-2">
                {events.map((event, index) => {
                  const nextEvent = events[index + 1];
                  const duration = nextEvent 
                    ? calculateDuration(nextEvent.ocppTimestamp, event.ocppTimestamp)
                    : null;
                  
                  return (
                    <div
                      key={event.id}
                      className="flex items-center space-x-3 rounded-md border border-gray-200 p-3 dark:border-gray-600"
                    >
                      {/* Status Indikator */}
                      <div
                        className={`h-4 w-4 rounded-full ${getStatusColor(event.status)}`}
                        title={event.status}
                      />
                      
                      {/* Status Text */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900 dark:text-white">
                            {event.status}
                          </span>
                          {duration && (
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              ({duration})
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {formatTimestamp(event.ocppTimestamp)}
                        </div>
                      </div>
                      
                      {/* Error Code falls vorhanden */}
                      {event.errorcode && event.errorcode !== "" && (
                        <div className="text-sm text-red-600 dark:text-red-400">
                          Error: {event.errorcode}
                        </div>
                      )}
                      
                      {/* EVSE ID */}
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {event.evseid}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Legende */}
      <div className="mt-6 border-t border-gray-200 pt-4 dark:border-gray-600">
        <h5 className="mb-2 text-sm font-medium text-gray-900 dark:text-white">
          Status Legende:
        </h5>
        <div className="flex flex-wrap gap-3">
          {Object.entries(colorMapping).map(([status, colorClass]) => (
            <div key={status} className="flex items-center space-x-2">
              <div className={`h-3 w-3 rounded-full ${colorClass}`} />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {status}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChargePointStatusTimeline;
