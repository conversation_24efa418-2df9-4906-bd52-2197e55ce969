"use client";

import React, { useMemo, useState } from "react";
import ReactECharts from "echarts-for-react";

// Type definition basierend auf dem Prisma Schema
interface OccpChargePointStatusData {
  id: string;
  status: string;
  errorcode: string;
  vendorid: string;
  chargepointid: string;
  connectornumber: number;
  locationid: string;
  evseid: string;
  ocppTimestamp: string;
}

interface ChargePointStatusTimelineProps {
  statusData: OccpChargePointStatusData[];
  chargePointId?: string; // Optional filter für einen spezifischen ChargePoint
  timeRange?: {
    start: Date;
    end: Date;
  };
}

// Zeitraum-Optionen
type TimeRangeOption = {
  label: string;
  value: string;
  hours: number;
};

const timeRangeOptions: TimeRangeOption[] = [
  { label: "Letzte Stunde", value: "1h", hours: 1 },
  { label: "Letzte 24 Stunden", value: "24h", hours: 24 },
  { label: "Letzte 7 Tage", value: "7d", hours: 24 * 7 },
  { label: "Letzte 30 Tage", value: "30d", hours: 24 * 30 },
];

// Status-Farben mapping für ECharts (Hex-Farben statt Tailwind-Klassen)
const colorMapping: Record<string, string> = {
  Available: "#86efac", // grün
  Charging: "#fde047", // gelb
  SuspendedEV: "#fdba74", // orange
  Preparing: "#d1d5db", // grau
  SuspendedEVSE: "#fb923c", // orange-400
  Finishing: "#e5e7eb", // grau-200
  Unavailable: "#fca5a5", // rot-300
  Faulted: "#f87171", // rot-400
};

// Interface für Timeline-Segmente
interface TimelineSegment {
  chargePointKey: string;
  status: string;
  startTime: Date;
  endTime: Date;
  duration: number; // in Millisekunden
}

// Interface für Chart-Daten
interface ChartDataPoint {
  name: string; // ChargePoint name
  value: [number, number, number]; // [startTime, endTime, yAxisIndex]
  itemStyle: {
    color: string;
  };
}

// Hilfsfunktion um Timeline-Segmente zu erstellen
const createTimelineSegments = (
  statusData: OccpChargePointStatusData[]
): TimelineSegment[] => {
  if (statusData.length === 0) return [];

  // Nach ChargePoint + Connector gruppieren
  const groups: Record<string, OccpChargePointStatusData[]> = {};
  statusData.forEach((item) => {
    const key = `${item.chargepointid}-${item.connectornumber}`;
    if (!groups[key]) groups[key] = [];
    groups[key].push(item);
  });

  const segments: TimelineSegment[] = [];

  Object.entries(groups).forEach(([chargePointKey, events]) => {
    // Chronologisch sortieren (älteste zuerst)
    const sortedEvents = events.sort(
      (a, b) => new Date(a.ocppTimestamp).getTime() - new Date(b.ocppTimestamp).getTime()
    );

    // Segmente zwischen Events erstellen
    for (let i = 0; i < sortedEvents.length - 1; i++) {
      const currentEvent = sortedEvents[i];
      const nextEvent = sortedEvents[i + 1];

      const startTime = new Date(currentEvent.ocppTimestamp);
      const endTime = new Date(nextEvent.ocppTimestamp);
      const duration = endTime.getTime() - startTime.getTime();

      if (duration > 0) {
        segments.push({
          chargePointKey,
          status: currentEvent.status,
          startTime,
          endTime,
          duration,
        });
      }
    }

    // Letztes Segment bis "jetzt" (falls vorhanden)
    if (sortedEvents.length > 0) {
      const lastEvent = sortedEvents[sortedEvents.length - 1];
      const startTime = new Date(lastEvent.ocppTimestamp);
      const endTime = new Date(); // jetzt
      const duration = endTime.getTime() - startTime.getTime();

      if (duration > 0) {
        segments.push({
          chargePointKey,
          status: lastEvent.status,
          startTime,
          endTime,
          duration,
        });
      }
    }
  });

  return segments;
};

// Hilfsfunktion um Mindestdauer zu garantieren (z.B. 5 Minuten)
const ensureMinimumDuration = (segments: TimelineSegment[]): TimelineSegment[] => {
  const minDurationMs = 5 * 60 * 1000; // 5 Minuten in Millisekunden

  return segments.map((segment) => {
    if (segment.duration < minDurationMs) {
      // Erweitere das Ende um die Mindestdauer
      const newEndTime = new Date(segment.startTime.getTime() + minDurationMs);
      return {
        ...segment,
        endTime: newEndTime,
        duration: minDurationMs,
      };
    }
    return segment;
  });
};

const ChargePointStatusTimeline: React.FC<ChargePointStatusTimelineProps> = ({
  statusData,
  chargePointId,
  timeRange: externalTimeRange,
}) => {
  // State für Zeitraum-Auswahl
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>("1h");

  // Berechne aktuellen Zeitraum
  const currentTimeRange = useMemo(() => {
    if (externalTimeRange) {
      return externalTimeRange;
    }

    const option = timeRangeOptions.find(opt => opt.value === selectedTimeRange);
    const hours = option?.hours || 24;
    const end = new Date();
    const start = new Date(end.getTime() - hours * 60 * 60 * 1000);

    return { start, end };
  }, [externalTimeRange, selectedTimeRange]);

  // Daten filtern
  const filteredData = useMemo(() => {
    let filtered = statusData;

    // Nach ChargePoint filtern falls angegeben
    if (chargePointId) {
      filtered = filtered.filter((item) => item.chargepointid === chargePointId);
    }

    // Nach Zeitraum filtern
    filtered = filtered.filter((item) => {
      const itemDate = new Date(item.ocppTimestamp);
      return itemDate >= currentTimeRange.start && itemDate <= currentTimeRange.end;
    });

    return filtered;
  }, [statusData, chargePointId, currentTimeRange]);

  // Timeline-Segmente erstellen
  const timelineSegments = useMemo(() => {
    const segments = createTimelineSegments(filteredData);
    return ensureMinimumDuration(segments);
  }, [filteredData]);

  // Chart-Daten für Timeline vorbereiten
  const chartData = useMemo(() => {
    if (timelineSegments.length === 0) return { chargePoints: [], series: [] };

    // Eindeutige ChargePoints sammeln und sortieren
    const chargePointKeys = Array.from(
      new Set(timelineSegments.map((s) => s.chargePointKey))
    ).sort();

    // Eindeutige Status sammeln
    const statusTypes = Array.from(
      new Set(timelineSegments.map((s) => s.status))
    ).sort();

    // Series für jeden Status erstellen
    const series = statusTypes.map((status) => {
      const data: ChartDataPoint[] = [];

      timelineSegments
        .filter((segment) => segment.status === status)
        .forEach((segment) => {
          const yIndex = chargePointKeys.indexOf(segment.chargePointKey);

          data.push({
            name: segment.chargePointKey,
            value: [
              segment.startTime.getTime(),
              segment.endTime.getTime(),
              yIndex
            ],
            itemStyle: {
              color: colorMapping[status] || "#d1d5db",
            },
          });
        });

      return {
        name: status,
        type: "custom" as const,
        renderItem: (params: any, api: any) => {
          const categoryIndex = api.value(2);
          const start = api.coord([api.value(0), categoryIndex]);
          const end = api.coord([api.value(1), categoryIndex]);
          const height = api.size([0, 1])[1] * 0.6;

          return {
            type: "rect",
            shape: {
              x: start[0],
              y: start[1] - height / 2,
              width: end[0] - start[0],
              height: height,
            },
            style: api.style(),
          };
        },
        data,
      };
    });

    return {
      chargePoints: chargePointKeys,
      series,
    };
  }, [timelineSegments]);

  if (filteredData.length === 0) {
    return (
      <div className="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          ChargePoint Status Timeline
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Keine Status-Daten verfügbar für den ausgewählten Zeitraum.
        </p>
      </div>
    );
  }

  // ECharts Options für Timeline
  const options = useMemo(() => ({
    title: {
      text: `ChargePoint Status Timeline (${timeRangeOptions.find(opt => opt.value === selectedTimeRange)?.label || 'Custom'})`,
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
      },
    },
    tooltip: {
      formatter: function (params: any) {
        const startTime = new Date(params.value[0]).toLocaleString("de-DE");
        const endTime = new Date(params.value[1]).toLocaleString("de-DE");
        const duration = Math.round((params.value[1] - params.value[0]) / (1000 * 60)); // Minuten

        return `
          <strong>${params.seriesName}</strong><br/>
          ChargePoint: ${params.name}<br/>
          Start: ${startTime}<br/>
          Ende: ${endTime}<br/>
          Dauer: ${duration} Minuten
        `;
      },
    },
    legend: {
      data: chartData.series.map((s) => s.name),
      top: 60,
      type: "scroll",
    },
    grid: {
      left: "15%",
      right: "4%",
      bottom: "3%",
      top: "20%",
      containLabel: true,
    },
    xAxis: {
      type: "time",
      min: currentTimeRange.start.getTime(),
      max: currentTimeRange.end.getTime(),
      axisLabel: {
        formatter: function (value: number) {
          return new Date(value).toLocaleString("de-DE", {
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
          });
        },
        fontSize: 10,
        rotate: 45,
      },
      splitLine: {
        show: true,
      },
    },
    yAxis: {
      type: "category",
      data: chartData.chargePoints.map((cp) => {
        const [cpId, connectorNum] = cp.split("-");
        return `${cpId}-C${connectorNum}`;
      }),
      axisLabel: {
        fontSize: 10,
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    series: chartData.series,
  }), [chartData, currentTimeRange, selectedTimeRange]);

  return (
    <div className="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              ChargePoint Status Timeline
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Timeline der Status-Änderungen ({filteredData.length} Events)
            </p>
          </div>

          {/* Zeitraum-Auswahl */}
          {!externalTimeRange && (
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Zeitraum:
              </label>
              <select
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value)}
                className="rounded-md border border-gray-300 bg-white px-3 py-1 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                {timeRangeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>

      <div className="h-96">
        <ReactECharts
          option={options}
          style={{ height: "100%", width: "100%" }}
          className="h-full"
        />
      </div>

      {/* Legende */}
      <div className="mt-4 border-t border-gray-200 pt-4 dark:border-gray-600">
        <h5 className="mb-2 text-sm font-medium text-gray-900 dark:text-white">
          Status Farben:
        </h5>
        <div className="flex flex-wrap gap-3">
          {Object.entries(colorMapping).map(([status, color]) => (
            <div key={status} className="flex items-center space-x-2">
              <div
                className="h-3 w-3 rounded-full"
                style={{ backgroundColor: color }}
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {status}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChargePointStatusTimeline;
