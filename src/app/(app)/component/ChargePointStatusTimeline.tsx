"use client";

import React, { useMemo } from "react";
import ReactECharts from "echarts-for-react";

// Type definition basierend auf dem Prisma Schema
interface OccpChargePointStatusData {
  id: string;
  status: string;
  errorcode: string;
  vendorid: string;
  chargepointid: string;
  connectornumber: number;
  locationid: string;
  evseid: string;
  ocppTimestamp: string;
}

interface ChargePointStatusTimelineProps {
  statusData: OccpChargePointStatusData[];
  chargePointId?: string; // Optional filter für einen spezifischen ChargePoint
  timeRange?: {
    start: Date;
    end: Date;
  };
}

// Status-Farben mapping für ECharts (Hex-Farben statt Tailwind-Klassen)
const colorMapping: Record<string, string> = {
  Available: "#86efac", // grün
  Charging: "#fde047", // gelb
  SuspendedEV: "#fdba74", // orange
  Preparing: "#d1d5db", // grau
  SuspendedEVSE: "#fb923c", // orange-400
  Finishing: "#e5e7eb", // grau-200
  Unavailable: "#fca5a5", // rot-300
  Faulted: "#f87171", // rot-400
};

// Interface für Timeline-Segmente
interface TimelineSegment {
  chargePointKey: string;
  status: string;
  startTime: Date;
  endTime: Date;
  duration: number; // in Millisekunden
  percentage: number; // Prozent der Gesamtzeit
}

// Hilfsfunktion um Timeline-Segmente zu erstellen
const createTimelineSegments = (
  statusData: OccpChargePointStatusData[]
): TimelineSegment[] => {
  if (statusData.length === 0) return [];

  // Nach ChargePoint + Connector gruppieren
  const groups: Record<string, OccpChargePointStatusData[]> = {};
  statusData.forEach((item) => {
    const key = `${item.chargepointid}-${item.connectornumber}`;
    if (!groups[key]) groups[key] = [];
    groups[key].push(item);
  });

  const segments: TimelineSegment[] = [];

  Object.entries(groups).forEach(([chargePointKey, events]) => {
    // Chronologisch sortieren (älteste zuerst)
    const sortedEvents = events.sort(
      (a, b) => new Date(a.ocppTimestamp).getTime() - new Date(b.ocppTimestamp).getTime()
    );

    // Segmente zwischen Events erstellen
    for (let i = 0; i < sortedEvents.length - 1; i++) {
      const currentEvent = sortedEvents[i];
      const nextEvent = sortedEvents[i + 1];

      const startTime = new Date(currentEvent.ocppTimestamp);
      const endTime = new Date(nextEvent.ocppTimestamp);
      const duration = endTime.getTime() - startTime.getTime();

      if (duration > 0) {
        segments.push({
          chargePointKey,
          status: currentEvent.status,
          startTime,
          endTime,
          duration,
          percentage: 0, // wird später berechnet
        });
      }
    }

    // Letztes Segment bis "jetzt" (falls vorhanden)
    if (sortedEvents.length > 0) {
      const lastEvent = sortedEvents[sortedEvents.length - 1];
      const startTime = new Date(lastEvent.ocppTimestamp);
      const endTime = new Date(); // jetzt
      const duration = endTime.getTime() - startTime.getTime();

      if (duration > 0) {
        segments.push({
          chargePointKey,
          status: lastEvent.status,
          startTime,
          endTime,
          duration,
          percentage: 0,
        });
      }
    }
  });

  return segments;
};

// Hilfsfunktion um Prozente zu berechnen und Mindest-1% zu garantieren
const calculatePercentages = (segments: TimelineSegment[]): TimelineSegment[] => {
  if (segments.length === 0) return [];

  // Gesamtdauer pro ChargePoint berechnen
  const totalDurationByChargePoint: Record<string, number> = {};
  segments.forEach((segment) => {
    if (!totalDurationByChargePoint[segment.chargePointKey]) {
      totalDurationByChargePoint[segment.chargePointKey] = 0;
    }
    totalDurationByChargePoint[segment.chargePointKey] += segment.duration;
  });

  // Prozente berechnen
  return segments.map((segment) => {
    const totalDuration = totalDurationByChargePoint[segment.chargePointKey];
    let percentage = totalDuration > 0 ? (segment.duration / totalDuration) * 100 : 0;

    // Mindestens 1% für Sichtbarkeit
    percentage = Math.max(percentage, 1);

    return {
      ...segment,
      percentage,
    };
  });
};

const ChargePointStatusTimeline: React.FC<ChargePointStatusTimelineProps> = ({
  statusData,
  chargePointId,
  timeRange,
}) => {
  // Daten filtern
  const filteredData = useMemo(() => {
    let filtered = statusData;

    // Nach ChargePoint filtern falls angegeben
    if (chargePointId) {
      filtered = filtered.filter((item) => item.chargepointid === chargePointId);
    }

    // Nach Zeitraum filtern falls angegeben
    if (timeRange) {
      filtered = filtered.filter((item) => {
        const itemDate = new Date(item.ocppTimestamp);
        return itemDate >= timeRange.start && itemDate <= timeRange.end;
      });
    }

    return filtered;
  }, [statusData, chargePointId, timeRange]);

  // Timeline-Segmente erstellen und Prozente berechnen
  const timelineSegments = useMemo(() => {
    const segments = createTimelineSegments(filteredData);
    return calculatePercentages(segments);
  }, [filteredData]);

  // Chart-Daten vorbereiten
  const chartData = useMemo(() => {
    if (timelineSegments.length === 0) return { chargePoints: [], series: [] };

    // Eindeutige ChargePoints sammeln
    const chargePointKeys = Array.from(
      new Set(timelineSegments.map((s) => s.chargePointKey))
    ).sort();

    // Eindeutige Status sammeln
    const statusTypes = Array.from(
      new Set(timelineSegments.map((s) => s.status))
    ).sort();

    // Series für jeden Status erstellen
    const series = statusTypes.map((status) => {
      const data = chargePointKeys.map((chargePointKey) => {
        // Alle Segmente für diesen ChargePoint und Status
        const segments = timelineSegments.filter(
          (s) => s.chargePointKey === chargePointKey && s.status === status
        );

        // Gesamtprozent für diesen Status
        const totalPercentage = segments.reduce((sum, s) => sum + s.percentage, 0);
        return Math.min(totalPercentage, 100); // Max 100%
      });

      return {
        name: status,
        type: "bar" as const,
        stack: "total",
        data,
        itemStyle: {
          color: colorMapping[status] || "#d1d5db",
        },
        emphasis: {
          focus: "series" as const,
        },
      };
    });

    return {
      chargePoints: chargePointKeys,
      series,
    };
  }, [timelineSegments]);

  if (filteredData.length === 0) {
    return (
      <div className="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          ChargePoint Status Timeline
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Keine Status-Daten verfügbar für den ausgewählten Zeitraum.
        </p>
      </div>
    );
  }

  // ECharts Options
  const options = useMemo(() => ({
    title: {
      text: "ChargePoint Status Timeline",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
      },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params: any) {
        let result = `<strong>${params[0].axisValue}</strong><br/>`;
        params.forEach((param: any) => {
          if (param.value > 0) {
            result += `${param.seriesName}: ${param.value.toFixed(1)}%<br/>`;
          }
        });
        return result;
      },
    },
    legend: {
      data: chartData.series.map((s) => s.name),
      top: 40,
      type: "scroll",
    },
    grid: {
      left: "15%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      max: 100,
      axisLabel: {
        formatter: "{value}%",
        fontSize: 10,
      },
      splitLine: {
        show: true,
      },
    },
    yAxis: {
      type: "category",
      data: chartData.chargePoints.map((cp) => {
        const [cpId, connectorNum] = cp.split("-");
        return `${cpId}-C${connectorNum}`;
      }),
      axisLabel: {
        fontSize: 10,
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    series: chartData.series,
  }), [chartData]);

  return (
    <div className="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          ChargePoint Status Timeline
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Zeitverteilung der Status für alle ChargePoints (letzte {filteredData.length} Events)
        </p>
      </div>

      <div className="h-96">
        <ReactECharts
          option={options}
          style={{ height: "100%", width: "100%" }}
          className="h-full"
        />
      </div>

      {/* Legende */}
      <div className="mt-4 border-t border-gray-200 pt-4 dark:border-gray-600">
        <h5 className="mb-2 text-sm font-medium text-gray-900 dark:text-white">
          Status Farben:
        </h5>
        <div className="flex flex-wrap gap-3">
          {Object.entries(colorMapping).map(([status, color]) => (
            <div key={status} className="flex items-center space-x-2">
              <div
                className="h-3 w-3 rounded-full"
                style={{ backgroundColor: color }}
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {status}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChargePointStatusTimeline;
