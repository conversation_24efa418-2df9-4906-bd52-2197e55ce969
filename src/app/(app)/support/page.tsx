import React from "react";
import Card from "~/component/card";
import ContactForm from "./components/ContactForm";
import Headline from "~/component/Headline";
import FAQAccordion from "~/app/(app)/testDashboard/components/FAQAccordion";
import type { FaqItem } from "~/types/CardHolderDashboardTypes/FAQTypes";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";


export default async function SupportPage() {

  const session = await getServerSession(authOptions);
  const role = session?.user?.role;
  const faqItems: FaqItem[] = [
    {
      question: "Ladevorgang startet nicht – was tun?",
      answer: (
        <ul>
          <li>RFID-Karte in der App auf „aktiv“ prüfen.</li>
          <li><PERSON><PERSON> trennen, 30 Sek. warten, neu stecken.</li>
          <li>Stations-ID am Aufkleber notieren und Support nennen.</li>
        </ul>
      ),
      category: "Laden",
      priority: 2,
    },
    {
      question: "Wo finde ich meine Rechnungen?",
      answer: <>App → <b>Profil → Rechnungen</b>. Zusätzlich per E-Mail an die hinterlegte Adresse.</>,
      category: "Abrechnung",
      priority: 1,
    },
    {
      question: "Karte verloren/defekt – wie sperre ich sie?",
      answer: <>App → <b>Profil → Karten</b> → Karte sperren, dort Ersatz bestellen.</>,
      category: "Karte",
    },
  ];
  return (
    <>
      <Headline title={"Support"} />
      <div className="grid grid-cols-1 gap-6 md:grid-cols-[400px_minmax(0,600px)]">
        {/* Linke Spalte: ContactForm */}
        <div className="w-full">
          <Card>
            <ContactForm />
          </Card>
        </div>

        {/* Rechte Spalte: FAQ */}
        {role === "CARD_HOLDER" && (
          <div className="w-full">
            <Card header_left={"FAQ"} className="max-w-xl">
              <FAQAccordion
                items={faqItems}
                categories={["Laden", "Abrechnung", "Karte"]}
                className="mt-2"
              />
            </Card>
          </div>
        )}
      </div>

    </>
  );

}


